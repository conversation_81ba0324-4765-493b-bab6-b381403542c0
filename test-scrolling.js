#!/usr/bin/env node

const { splitScreenDashboard } = require('./dist/utils/splitScreenDashboard');
const { ethers } = require('ethers');

console.log('🖱️  Testing Split Screen Dashboard Scrolling...\n');

// Set environment variable to enable split screen
process.env.SPLIT_SCREEN_DASHBOARD = 'true';

// Initialize dashboard with test data
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia',
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 1250,
  relevantTransactions: 89,
  opportunitiesFound: 23,
  opportunitiesExecuted: 8,
  totalProfit: ethers.parseEther('0.0456'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'],
    dexes: ['Uniswap V3', 'Curve', 'Sushiswap'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [
    {
      timestamp: Date.now() - 30000,
      type: 'flashloan',
      profit: ethers.parseEther('0.0123'),
      gasUsed: ethers.parseEther('0.0045'),
      txHash: '******************************************',
      confidence: 95,
      details: 'USDC → WETH'
    }
  ],
  errors: 2,
  lastError: 'Gas price too high'
};

// Start the split screen dashboard
splitScreenDashboard.updateDashboardData(testData);
splitScreenDashboard.start();

console.log('🖱️  Split screen dashboard with scrolling started!');
console.log('');
console.log('📋 SCROLLING CONTROLS:');
console.log('├── Mouse Wheel: Scroll in either panel');
console.log('├── Click: Focus a panel (yellow border = focused)');
console.log('├── Tab: Switch focus between panels');
console.log('├── Arrow Keys: Scroll in focused panel');
console.log('├── Page Up/Down: Fast scroll in focused panel');
console.log('├── "r": Refresh display');
console.log('└── "q", Escape, or Ctrl+C: Quit');
console.log('');
console.log('🧪 Generating lots of logs to test scrolling...');

// Generate many logs to test scrolling
let logCount = 0;
const generateLogs = () => {
  const logTypes = [
    () => splitScreenDashboard.addLog('INFO', `Log entry #${++logCount}: Mempool transaction detected`, { hash: `0x${Math.random().toString(16).slice(2, 18)}` }),
    () => splitScreenDashboard.addLog('DEBUG', `Log entry #${++logCount}: Gas price analysis`, { gasPrice: `${Math.floor(Math.random() * 50 + 20)} gwei` }),
    () => splitScreenDashboard.addLog('WARN', `Log entry #${++logCount}: High gas price warning`, { threshold: '50 gwei' }),
    () => splitScreenDashboard.addLog('INFO', `Log entry #${++logCount}: Arbitrage opportunity found`, { profit: `${(Math.random() * 0.1).toFixed(4)} ETH` }),
    () => splitScreenDashboard.addLog('ERROR', `Log entry #${++logCount}: RPC connection failed`, { provider: 'Infura' }),
    () => splitScreenDashboard.addLog('INFO', `Log entry #${++logCount}: Block ${18500000 + Math.floor(Math.random() * 100)} processed`),
    () => splitScreenDashboard.addLog('DEBUG', `Log entry #${++logCount}: Flashloan simulation`, { amount: `${Math.floor(Math.random() * 1000)} USDC` }),
    () => splitScreenDashboard.addLog('INFO', `Log entry #${++logCount}: MEV bundle submitted`, { blockNumber: 18500000 + Math.floor(Math.random() * 100) }),
    () => splitScreenDashboard.addLog('WARN', `Log entry #${++logCount}: Slippage detected`, { expected: '0.5%', actual: '0.8%' }),
    () => splitScreenDashboard.addLog('INFO', `Log entry #${++logCount}: Profit calculation completed`, { profit: `${(Math.random() * 0.05).toFixed(4)} ETH` })
  ];

  // Generate initial batch of logs
  for (let i = 0; i < 30; i++) {
    const logType = logTypes[Math.floor(Math.random() * logTypes.length)];
    logType();
  }
};

// Generate initial logs
generateLogs();

// Continue generating logs every 2 seconds
const logInterval = setInterval(() => {
  const logType = Math.floor(Math.random() * 3);
  
  if (logType === 0) {
    splitScreenDashboard.addLog('INFO', `Log entry #${++logCount}: New transaction in mempool`, { 
      hash: `0x${Math.random().toString(16).slice(2, 18)}`,
      gasPrice: `${Math.floor(Math.random() * 50 + 20)} gwei`
    });
  } else if (logType === 1) {
    splitScreenDashboard.addLog('DEBUG', `Log entry #${++logCount}: Scanning for opportunities...`, {
      dex: ['Uniswap', 'Sushiswap', 'Curve'][Math.floor(Math.random() * 3)],
      token: ['WETH', 'USDC', 'DAI'][Math.floor(Math.random() * 3)]
    });
  } else {
    splitScreenDashboard.addLog('WARN', `Log entry #${++logCount}: Network congestion detected`, {
      gasPrice: `${Math.floor(Math.random() * 100 + 50)} gwei`,
      congestion: `${Math.floor(Math.random() * 50 + 50)}%`
    });
  }
}, 2000);

// Update dashboard stats occasionally
const statsInterval = setInterval(() => {
  testData.totalTransactions += Math.floor(Math.random() * 5);
  testData.relevantTransactions += Math.floor(Math.random() * 2);
  testData.lastActivity = Date.now();
  testData.currentBlock += Math.floor(Math.random() * 2);
  
  splitScreenDashboard.updateDashboardData(testData);
}, 5000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(logInterval);
  clearInterval(statsInterval);
  splitScreenDashboard.stop();
  console.log('\n👋 Scrolling test completed!');
  console.log('✅ Mouse wheel scrolling should now work in both panels');
  console.log('✅ Click to focus panels (yellow border indicates focus)');
  console.log('✅ Tab to switch focus, arrow keys to scroll');
  process.exit(0);
});

// Keep the process running
process.stdin.resume();
