/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface FlashloanArbitrageInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "ADDRESSES_PROVIDER"
      | "CHAIN_ID"
      | "POOL"
      | "emergencyWithdraw"
      | "executeFlashloanArbitrage"
      | "executeOperation"
      | "owner"
      | "renounceOwnership"
      | "transferOwnership"
      | "withdrawProfits"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ArbitrageExecuted"
      | "FlashloanExecuted"
      | "OwnershipTransferred"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "ADDRESSES_PROVIDER",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "CHAIN_ID", values?: undefined): string;
  encodeFunctionData(functionFragment: "POOL", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "executeFlashloanArbitrage",
    values: [AddressLike, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "executeOperation",
    values: [AddressLike, BigNumberish, BigNumberish, AddressLike, BytesLike]
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "withdrawProfits",
    values: [AddressLike]
  ): string;

  decodeFunctionResult(
    functionFragment: "ADDRESSES_PROVIDER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "CHAIN_ID", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "POOL", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeFlashloanArbitrage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeOperation",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "withdrawProfits",
    data: BytesLike
  ): Result;
}

export namespace ArbitrageExecutedEvent {
  export type InputTuple = [
    asset: AddressLike,
    amount: BigNumberish,
    profit: BigNumberish
  ];
  export type OutputTuple = [asset: string, amount: bigint, profit: bigint];
  export interface OutputObject {
    asset: string;
    amount: bigint;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace FlashloanExecutedEvent {
  export type InputTuple = [
    asset: AddressLike,
    amount: BigNumberish,
    premium: BigNumberish
  ];
  export type OutputTuple = [asset: string, amount: bigint, premium: bigint];
  export interface OutputObject {
    asset: string;
    amount: bigint;
    premium: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface FlashloanArbitrage extends BaseContract {
  connect(runner?: ContractRunner | null): FlashloanArbitrage;
  waitForDeployment(): Promise<this>;

  interface: FlashloanArbitrageInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  ADDRESSES_PROVIDER: TypedContractMethod<[], [string], "view">;

  CHAIN_ID: TypedContractMethod<[], [bigint], "view">;

  POOL: TypedContractMethod<[], [string], "view">;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;

  executeFlashloanArbitrage: TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [void],
    "nonpayable"
  >;

  executeOperation: TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      premium: BigNumberish,
      initiator: AddressLike,
      params: BytesLike
    ],
    [boolean],
    "nonpayable"
  >;

  owner: TypedContractMethod<[], [string], "view">;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  withdrawProfits: TypedContractMethod<
    [token: AddressLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "ADDRESSES_PROVIDER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "CHAIN_ID"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "POOL"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "executeFlashloanArbitrage"
  ): TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "executeOperation"
  ): TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      premium: BigNumberish,
      initiator: AddressLike,
      params: BytesLike
    ],
    [boolean],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "withdrawProfits"
  ): TypedContractMethod<[token: AddressLike], [void], "nonpayable">;

  getEvent(
    key: "ArbitrageExecuted"
  ): TypedContractEvent<
    ArbitrageExecutedEvent.InputTuple,
    ArbitrageExecutedEvent.OutputTuple,
    ArbitrageExecutedEvent.OutputObject
  >;
  getEvent(
    key: "FlashloanExecuted"
  ): TypedContractEvent<
    FlashloanExecutedEvent.InputTuple,
    FlashloanExecutedEvent.OutputTuple,
    FlashloanExecutedEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;

  filters: {
    "ArbitrageExecuted(address,uint256,uint256)": TypedContractEvent<
      ArbitrageExecutedEvent.InputTuple,
      ArbitrageExecutedEvent.OutputTuple,
      ArbitrageExecutedEvent.OutputObject
    >;
    ArbitrageExecuted: TypedContractEvent<
      ArbitrageExecutedEvent.InputTuple,
      ArbitrageExecutedEvent.OutputTuple,
      ArbitrageExecutedEvent.OutputObject
    >;

    "FlashloanExecuted(address,uint256,uint256)": TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;
    FlashloanExecuted: TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
  };
}
