/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  FlashloanArbitrage,
  FlashloanArbitrageInterface,
} from "../../../contracts/FlashloanArbitrage.sol/FlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_addressProvider",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeFlashloanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type FlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: FlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class FlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: FlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(_addressProvider, overrides || {});
  }
  override deploy(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_addressProvider, overrides || {}) as Promise<
      FlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): FlashloanArbitrage__factory {
    return super.connect(runner) as FlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): FlashloanArbitrageInterface {
    return new Interface(_abi) as FlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): FlashloanArbitrage {
    return new Contract(address, _abi, runner) as unknown as FlashloanArbitrage;
  }
}
