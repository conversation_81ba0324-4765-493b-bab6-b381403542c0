#!/usr/bin/env node

const { splitScreenDashboard } = require('./dist/utils/splitScreenDashboard');
const { ethers } = require('ethers');

console.log('📋 Testing Split Screen Dashboard Copy/Paste Functionality...\n');

// Set environment variable to enable split screen
process.env.SPLIT_SCREEN_DASHBOARD = 'true';

// Initialize dashboard with test data
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia',
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 1250,
  relevantTransactions: 89,
  opportunitiesFound: 23,
  opportunitiesExecuted: 8,
  totalProfit: ethers.parseEther('0.0456'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'],
    dexes: ['Uniswap V3', 'Curve', 'Sushiswap'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [
    {
      timestamp: Date.now() - 30000,
      type: 'flashloan',
      profit: ethers.parseEther('0.0123'),
      gasUsed: ethers.parseEther('0.0045'),
      txHash: '******************************************',
      confidence: 95,
      details: 'USDC → WETH'
    }
  ],
  errors: 2,
  lastError: 'Gas price too high'
};

// Start the split screen dashboard
splitScreenDashboard.updateDashboardData(testData);
splitScreenDashboard.start();

console.log('📋 Split screen dashboard with copy/paste functionality started!');
console.log('');
console.log('🎯 COPY/PASTE TESTING INSTRUCTIONS:');
console.log('');
console.log('1. 🖱️  MOUSE MODE (Default):');
console.log('   ├── Mouse wheel scrolling works in both panels');
console.log('   ├── Click to focus panels (yellow border = focused)');
console.log('   ├── Tab key switches focus between panels');
console.log('   └── Text selection is disabled for mouse interaction');
console.log('');
console.log('2. 📝 TEXT MODE (Press "m" to toggle):');
console.log('   ├── Mouse wheel scrolling is disabled');
console.log('   ├── Click and drag to select text');
console.log('   ├── Use Ctrl+C to copy selected text');
console.log('   ├── Use Ctrl+V to paste (in other applications)');
console.log('   └── Right-click for context menu (if supported)');
console.log('');
console.log('🔄 TOGGLE CONTROLS:');
console.log('├── Press "m": Switch between Mouse Mode and Text Mode');
console.log('├── Press "r": Refresh display');
console.log('├── Press "Tab": Switch focus between panels');
console.log('└── Press "q", Escape, or Ctrl+C: Quit');
console.log('');
console.log('📊 WHAT TO TEST:');
console.log('1. Start in Mouse Mode - try scrolling with mouse wheel');
console.log('2. Press "m" to switch to Text Mode');
console.log('3. Try to select text in the log panel (right side)');
console.log('4. Copy the selected text with Ctrl+C');
console.log('5. Paste it in another application to verify');
console.log('6. Press "m" again to return to Mouse Mode');
console.log('7. Verify mouse scrolling works again');
console.log('');

// Generate many logs with copyable content
let logCount = 0;
const generateCopyableContent = () => {
  const copyableData = [
    'Transaction Hash: ******************************************',
    'Profit: 0.0123 ETH | Gas Used: 0.0045 ETH | Confidence: 95%',
    'DEX Arbitrage: Uniswap V3 → Curve | Token: USDC → WETH',
    'Block Number: 18500123 | Gas Price: 25 gwei | Timestamp: ' + new Date().toISOString(),
    'Flashloan Amount: 5000 USDC | Profit Margin: 1.5% | Execution Time: 12.3s',
    'MEV Bundle: 3 transactions | Total Gas: 450,000 | Bundle Profit: 0.0234 ETH',
    'Contract Address: ******************************************',
    'RPC Endpoint: https://hardhat-local:8545 | Network: Chain 31337',
    'Wallet Balance: 10002.78265073 ETH | Available for Trading: 5000 USDC',
    'Strategy: Balancer V2 Flashloan | Vault: ******************************************'
  ];

  const logTypes = [
    () => splitScreenDashboard.addLog('INFO', `${copyableData[0]} - Transaction detected`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[1]} - Profit calculation completed`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[2]} - Arbitrage opportunity executed`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('DEBUG', `${copyableData[3]} - Block processed`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[4]} - Flashloan executed successfully`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[5]} - Bundle submitted to mempool`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('DEBUG', `${copyableData[6]} - Contract interaction`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[7]} - Network connection established`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[8]} - Wallet status updated`, { count: ++logCount }),
    () => splitScreenDashboard.addLog('INFO', `${copyableData[9]} - Strategy initialized`, { count: ++logCount })
  ];

  // Generate initial batch of logs
  for (let i = 0; i < 25; i++) {
    const logType = logTypes[Math.floor(Math.random() * logTypes.length)];
    logType();
  }
};

// Generate initial copyable content
generateCopyableContent();

// Add some specific test content
splitScreenDashboard.addLog('INFO', '🧪 TEST CONTENT FOR COPY/PASTE:');
splitScreenDashboard.addLog('INFO', '═══════════════════════════════════════');
splitScreenDashboard.addLog('INFO', 'Transaction: ******************************************');
splitScreenDashboard.addLog('INFO', 'Profit: 0.0456 ETH | Gas: 0.0032 ETH');
splitScreenDashboard.addLog('INFO', 'Strategy: Flashloan Arbitrage');
splitScreenDashboard.addLog('INFO', 'DEX Pair: Uniswap V3 ↔ Curve Finance');
splitScreenDashboard.addLog('INFO', 'Token Flow: USDC → WETH → DAI → USDC');
splitScreenDashboard.addLog('INFO', 'Execution Time: 2.1 seconds');
splitScreenDashboard.addLog('INFO', 'Confidence Score: 98.5%');
splitScreenDashboard.addLog('INFO', '═══════════════════════════════════════');
splitScreenDashboard.addLog('INFO', '👆 Try selecting and copying this content!');

// Continue generating logs every 3 seconds
const logInterval = setInterval(() => {
  const logType = Math.floor(Math.random() * 4);
  
  if (logType === 0) {
    splitScreenDashboard.addLog('INFO', `New MEV opportunity detected: ${++logCount}`, { 
      hash: `0x${Math.random().toString(16).slice(2, 18)}`,
      profit: `${(Math.random() * 0.1).toFixed(4)} ETH`
    });
  } else if (logType === 1) {
    splitScreenDashboard.addLog('DEBUG', `Gas analysis completed: ${++logCount}`, {
      gasPrice: `${Math.floor(Math.random() * 50 + 20)} gwei`,
      recommendation: 'Execute transaction'
    });
  } else if (logType === 2) {
    splitScreenDashboard.addLog('WARN', `High slippage detected: ${++logCount}`, {
      expected: '0.5%',
      actual: `${(Math.random() * 2 + 0.5).toFixed(1)}%`
    });
  } else {
    splitScreenDashboard.addLog('INFO', `Block ${18500000 + Math.floor(Math.random() * 1000)} processed: ${++logCount}`, {
      transactions: Math.floor(Math.random() * 200 + 50),
      relevantTxs: Math.floor(Math.random() * 10)
    });
  }
}, 3000);

// Update dashboard stats occasionally
const statsInterval = setInterval(() => {
  testData.totalTransactions += Math.floor(Math.random() * 5);
  testData.relevantTransactions += Math.floor(Math.random() * 2);
  testData.lastActivity = Date.now();
  testData.currentBlock += Math.floor(Math.random() * 2);
  
  splitScreenDashboard.updateDashboardData(testData);
}, 5000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(logInterval);
  clearInterval(statsInterval);
  splitScreenDashboard.stop();
  console.log('\n👋 Copy/paste test completed!');
  console.log('');
  console.log('✅ FEATURES TESTED:');
  console.log('├── Mouse Mode: Scrolling and navigation');
  console.log('├── Text Mode: Text selection and copy/paste');
  console.log('├── Mode Toggle: Press "m" to switch modes');
  console.log('└── Visual Feedback: Title bar shows current mode');
  console.log('');
  console.log('🎯 Copy/paste functionality is now available!');
  process.exit(0);
});

// Keep the process running
process.stdin.resume();
