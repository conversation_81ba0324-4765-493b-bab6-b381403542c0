#!/usr/bin/env node

const { splitScreenDashboard } = require('./dist/utils/splitScreenDashboard');
const { ethers } = require('ethers');

console.log('📋 Testing Improved Copy/Paste Functionality...\n');

// Set environment variable to enable split screen
process.env.SPLIT_SCREEN_DASHBOARD = 'true';

// Initialize dashboard with test data
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia',
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 1250,
  relevantTransactions: 89,
  opportunitiesFound: 23,
  opportunitiesExecuted: 8,
  totalProfit: ethers.parseEther('0.0456'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'],
    dexes: ['Uniswap V3', 'Curve', 'Sushiswap'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [
    {
      timestamp: Date.now() - 30000,
      type: 'flashloan',
      profit: ethers.parseEther('0.0123'),
      gasUsed: ethers.parseEther('0.0045'),
      txHash: '******************************************',
      confidence: 95,
      details: 'USDC → WETH'
    }
  ],
  errors: 2,
  lastError: 'Gas price too high'
};

// Start the split screen dashboard
splitScreenDashboard.updateDashboardData(testData);
splitScreenDashboard.start();

console.log('📋 Improved copy/paste functionality test started!');
console.log('');
console.log('🎯 TESTING INSTRUCTIONS:');
console.log('');
console.log('1. 🖱️  MOUSE MODE (Default):');
console.log('   ├── Dashboard stays intact with status on left, logs on right');
console.log('   ├── Mouse wheel scrolling works in both panels');
console.log('   ├── Click to focus panels (yellow border = focused)');
console.log('   └── Tab key switches focus between panels');
console.log('');
console.log('2. 📝 TEXT MODE (Press "m" to toggle):');
console.log('   ├── Dashboard interface remains intact');
console.log('   ├── Instructions appear in the log panel');
console.log('   ├── Mouse selection should work in the log area');
console.log('   ├── Use Ctrl+C to copy selected text');
console.log('   └── Press "m" again to return to mouse mode');
console.log('');
console.log('🔄 CONTROLS:');
console.log('├── Press "m": Toggle between Mouse Mode and Text Mode');
console.log('├── Press "Tab": Switch focus between panels');
console.log('├── Press "r": Refresh display');
console.log('└── Press "q", Escape, or Ctrl+C: Quit');
console.log('');

// Add some copyable content to the logs
splitScreenDashboard.addLog('INFO', '🧪 COPYABLE TEST CONTENT:');
splitScreenDashboard.addLog('INFO', '═══════════════════════════════════════');
splitScreenDashboard.addLog('INFO', 'Transaction Hash: ******************************************');
splitScreenDashboard.addLog('INFO', 'Contract Address: ******************************************');
splitScreenDashboard.addLog('INFO', 'Profit Amount: 0.0456 ETH');
splitScreenDashboard.addLog('INFO', 'Gas Price: 25 gwei');
splitScreenDashboard.addLog('INFO', 'DEX Pair: Uniswap V3 ↔ Curve Finance');
splitScreenDashboard.addLog('INFO', 'Token Flow: USDC → WETH → DAI → USDC');
splitScreenDashboard.addLog('INFO', 'Execution Time: 2.1 seconds');
splitScreenDashboard.addLog('INFO', 'Confidence Score: 98.5%');
splitScreenDashboard.addLog('INFO', '═══════════════════════════════════════');
splitScreenDashboard.addLog('INFO', '👆 Try pressing "m" then selecting this text!');

// Generate more logs for testing
let logCount = 0;
const generateLogs = () => {
  const copyableData = [
    'MEV Opportunity: 0x' + Math.random().toString(16).slice(2, 18) + ' | Profit: ' + (Math.random() * 0.1).toFixed(4) + ' ETH',
    'Gas Analysis: Current ' + Math.floor(Math.random() * 50 + 20) + ' gwei | Recommended: Execute',
    'Flashloan: ' + Math.floor(Math.random() * 5000 + 1000) + ' USDC | Success Rate: ' + Math.floor(Math.random() * 30 + 70) + '%',
    'Block ' + (18500000 + Math.floor(Math.random() * 1000)) + ' | Transactions: ' + Math.floor(Math.random() * 200 + 50),
    'Arbitrage: ' + ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'][Math.floor(Math.random() * 3)] + ' | Spread: ' + (Math.random() * 2).toFixed(2) + '%'
  ];

  const logType = copyableData[Math.floor(Math.random() * copyableData.length)];
  splitScreenDashboard.addLog('INFO', `${logType}`, { count: ++logCount });
};

// Generate initial logs
for (let i = 0; i < 15; i++) {
  generateLogs();
}

// Continue generating logs every 3 seconds
const logInterval = setInterval(() => {
  generateLogs();
}, 3000);

// Update dashboard stats occasionally
const statsInterval = setInterval(() => {
  testData.totalTransactions += Math.floor(Math.random() * 5);
  testData.relevantTransactions += Math.floor(Math.random() * 2);
  testData.lastActivity = Date.now();
  testData.currentBlock += Math.floor(Math.random() * 2);
  
  splitScreenDashboard.updateDashboardData(testData);
}, 5000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(logInterval);
  clearInterval(statsInterval);
  splitScreenDashboard.stop();
  console.log('\n👋 Improved copy/paste test completed!');
  console.log('');
  console.log('✅ FEATURES TESTED:');
  console.log('├── Dashboard interface remains intact when toggling modes');
  console.log('├── Mouse Mode: Full scrolling and navigation');
  console.log('├── Text Mode: Instructions appear in log panel');
  console.log('├── Text selection should work in log area');
  console.log('└── Mode toggle with "m" key');
  console.log('');
  console.log('🎯 The improved copy/paste solution is ready!');
  process.exit(0);
});

// Keep the process running
process.stdin.resume();
