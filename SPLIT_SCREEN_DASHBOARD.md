# Split-Screen Dashboard for MEV Bot

## Overview

The MEV Bot now features a modern split-screen terminal interface that provides real-time monitoring with:
- **Left Panel**: Live status dashboard with bot statistics and configuration
- **Right Panel**: Real-time log streaming with color-coded messages

## Features

### Status Dashboard (Left Panel)
- **Status Overview**: Bot running state, network info, block numbers, uptime
- **Strategy Status**: Enabled/disabled strategies (Flashloan, MEV-Share, Arbitrage)
- **Statistics**: Transaction counts, opportunities found/executed, profit tracking
- **Configuration**: Token pairs, DEXes, profit thresholds, gas limits
- **Recent Transactions**: Latest successful MEV transactions with profit details

### Live Logs (Right Panel)
- **Real-time Streaming**: All bot logs appear instantly
- **Color Coding**: Different log levels (ERROR, WARN, INFO, DEBUG) with distinct colors
- **Timestamps**: Precise timing for all log entries
- **Scrollable**: Navigate through log history with arrow keys

### Interactive Controls
- **`q`, `Escape`, `Ctrl+C`**: Quit the dashboard
- **`r`**: Refresh the display
- **`m`**: Toggle between Mouse Mode and Text Mode for copy/paste
- **`Tab`**: Switch focus between left and right panels
- **Arrow Keys (`↑`/`↓`)**: Scroll through content in the focused panel
- **Page Up/Page Down**: Fast scroll through content in the focused panel

#### Mouse Mode (Default)
- **Mouse Click**: Click on a panel to focus it
- **Mouse Wheel**: Scroll through content in either panel
- **Visual Focus**: Focused panel has a yellow border

#### Text Mode (Copy/Paste)
- **Text Selection**: Click and drag to select text
- **Copy/Paste**: Use Ctrl+C/Ctrl+V or right-click context menu
- **Mouse Wheel**: Disabled to allow text selection
- **Note**: Press `m` to return to Mouse Mode for scrolling

## Usage

### Enable Split-Screen Dashboard

#### Method 1: Use the new npm script
```bash
npm run dev
```
The split-screen dashboard is now enabled by default for `npm run dev`.

#### Method 2: Environment variable
```bash
SPLIT_SCREEN_DASHBOARD=true npm run dev:classic
```

#### Method 3: Use the demo script
```bash
./demo-split-screen.js
```

### Classic Dashboard (Old Style)
If you prefer the original full-screen dashboard:
```bash
npm run dev:classic
```

## Technical Implementation

### Library Used
- **blessed**: A comprehensive terminal UI library for Node.js
- Provides advanced terminal features like split panes, scrolling, and interactive controls

### Architecture
- `SplitScreenDashboard` class manages the terminal UI
- Integrates with existing `logger` and `enhancedLogger` systems
- Real-time data updates from `statusDashboard`
- Non-blocking operation with the main MEV bot

### Files Added/Modified
- `src/utils/splitScreenDashboard.ts` - Main split-screen implementation
- `src/utils/statusDashboard.ts` - Integration with split-screen mode
- `src/utils/logger.ts` - Log forwarding to split-screen
- `src/utils/enhancedLogger.ts` - Enhanced log forwarding
- `test/test-split-screen-dashboard.js` - Test suite
- `demo-split-screen.js` - Demo script
- `package.json` - New npm scripts

## Configuration

### Environment Variables
- `SPLIT_SCREEN_DASHBOARD=true` - Enable split-screen mode
- `NODE_ENV=development` - Auto-enable split-screen in development

### Dashboard Settings
The split-screen dashboard can be configured in `src/utils/splitScreenDashboard.ts`:
```typescript
const config = {
  title: 'MEV Bot Dashboard',
  refreshRate: 1000,        // Update interval in ms
  maxLogLines: 1000,        // Max log entries to keep
  maxStatusLines: 50        // Max status lines to display
};
```

## Testing

### Test the Split-Screen Dashboard
```bash
npm run test:split-screen
```

### Demo with Simulated Data
```bash
./demo-split-screen.js
```

### Test with Real MEV Bot
```bash
SPLIT_SCREEN_DASHBOARD=true npm run dev
```

## Benefits

1. **Better Monitoring**: See both status and logs simultaneously
2. **Improved UX**: Modern terminal interface with interactive controls
3. **Real-time Updates**: Instant feedback on bot performance
4. **Space Efficient**: Optimal use of terminal real estate
5. **Professional Look**: Clean, organized display suitable for production monitoring

## Compatibility

- **Node.js**: 16+ (tested with Node.js 18+)
- **Terminal**: Works with most modern terminals (iTerm2, Terminal.app, Windows Terminal, etc.)
- **OS**: macOS, Linux, Windows
- **Screen Size**: Optimized for terminals 120+ columns wide

## Troubleshooting

### Terminal Too Small
If your terminal is too small, the dashboard may not display correctly. Recommended minimum size: 120x30 characters.

### Colors Not Showing
Ensure your terminal supports ANSI colors. Most modern terminals do by default.

### Performance Issues
If you experience performance issues, you can:
1. Increase the refresh rate in the configuration
2. Reduce the number of log lines kept in memory
3. Use the classic dashboard mode instead

## Future Enhancements

Potential improvements for future versions:
- Mouse support for clicking and scrolling
- Configurable panel sizes
- Additional dashboard panels (gas tracker, profit charts)
- Export logs to file from the interface
- Remote monitoring capabilities
