import blessed from 'blessed';
import chalk from 'chalk';
import { ethers } from 'ethers';
import { LogEntry, LogLevel } from '../types';

export interface SplitScreenConfig {
  title: string;
  refreshRate: number; // milliseconds
  maxLogLines: number;
  maxStatusLines: number;
}

export interface DashboardData {
  // Network status
  currentBlock: number;
  networkName: string;
  
  // Bot status
  isRunning: boolean;
  uptime: number;
  lastActivity: number;
  
  // Strategy status
  flashloanEnabled: boolean;
  mevShareEnabled: boolean;
  arbitrageEnabled: boolean;
  
  // Statistics
  totalTransactions: number;
  relevantTransactions: number;
  opportunitiesFound: number;
  opportunitiesExecuted: number;
  totalProfit: bigint;
  avgGasPrice: bigint;
  
  // Configuration
  configuration: {
    tokenPairs: string[];
    dexes: string[];
    minProfitThreshold: string;
    maxGasPrice: string;
  };
  
  // Successful transactions
  successfulTransactions: Array<{
    timestamp: number;
    type: string;
    profit: bigint;
    gasUsed: bigint;
    txHash?: string;
    confidence?: number;
    details?: string;
  }>;
  
  // Error tracking
  errors: number;
  lastError?: string;
}

export class SplitScreenDashboard {
  private screen!: blessed.Widgets.Screen;
  private statusBox!: blessed.Widgets.BoxElement;
  private logBox!: blessed.Widgets.Log;
  private titleBox!: blessed.Widgets.BoxElement;
  private isRunning: boolean = false;
  private refreshTimer?: NodeJS.Timeout;
  private config: SplitScreenConfig;
  private dashboardData: DashboardData;
  private logEntries: LogEntry[] = [];
  private mouseInteractionMode: boolean = true;

  constructor(config: Partial<SplitScreenConfig> = {}) {
    this.config = {
      title: 'MEV Bot Dashboard',
      refreshRate: 1000,
      maxLogLines: 1000,
      maxStatusLines: 50,
      ...config
    };

    this.dashboardData = this.getDefaultDashboardData();
    this.initializeScreen();
  }

  private getDefaultDashboardData(): DashboardData {
    return {
      currentBlock: 0,
      networkName: 'Unknown',
      isRunning: false,
      uptime: 0,
      lastActivity: 0,
      flashloanEnabled: false,
      mevShareEnabled: false,
      arbitrageEnabled: false,
      totalTransactions: 0,
      relevantTransactions: 0,
      opportunitiesFound: 0,
      opportunitiesExecuted: 0,
      totalProfit: BigInt(0),
      avgGasPrice: BigInt(0),
      configuration: {
        tokenPairs: [],
        dexes: [],
        minProfitThreshold: '0',
        maxGasPrice: '0'
      },
      successfulTransactions: [],
      errors: 0,
      lastError: undefined
    };
  }

  private initializeScreen(): void {
    try {
      // Capture stdout to prevent artifacts during initialization
      const originalWrite = process.stdout.write;
      let capturedOutput = '';

      // Temporarily capture stdout to prevent artifacts
      process.stdout.write = function(chunk: any, encoding?: any, callback?: any) {
        if (typeof chunk === 'string') {
          // Filter out problematic ANSI sequences that cause artifacts
          const filtered = chunk
            .replace(/\x1b\[\?1049h/g, '') // Alternate screen buffer
            .replace(/\x1b\[\?1h=/g, '')   // Application cursor keys
            .replace(/\x1b\[1;9r/g, '')    // Set scroll region
            .replace(/\x1b\[\?25l/g, '')   // Hide cursor
            .replace(/\x1b\[H\x1b\[2J/g, '') // Clear screen
            .replace(/\x1b\[0m/g, '')      // Reset attributes
            .replace(/\x1b\[\d+;\d+H/g, '') // Cursor positioning
            .replace(/\x1b\[\d+[ABCD]/g, '') // Cursor movement
            .replace(/\x1b\[\?1000[hl]/g, '') // Mouse tracking
            .replace(/\x1b\[\?1002[hl]/g, '') // Button event tracking
            .replace(/\x1b\[\?1003[hl]/g, ''); // Any event tracking

          if (filtered.length > 0 && !filtered.match(/^\x1b\[/)) {
            capturedOutput += filtered;
          }
        }

        if (callback) callback();
        return true;
      };

      // Create the main screen with minimal options to prevent artifacts
      this.screen = blessed.screen({
        smartCSR: false, // Disable smart cursor save/restore
        title: this.config.title,
        dockBorders: true,
        fullUnicode: false, // Disable full unicode to prevent artifacts
        autoPadding: true,
        mouse: false, // Completely disable mouse
        sendFocus: false, // Disable focus events
        grabKeys: false,
        input: process.stdin,
        output: process.stdout,
        terminal: 'xterm-256color',
        debug: false,
        warnings: false
      });

      // Restore stdout after initialization
      setTimeout(() => {
        process.stdout.write = originalWrite;

        // Output any captured legitimate output
        if (capturedOutput.trim()) {
          process.stdout.write(capturedOutput);
        }
      }, 100);

      // Disable problematic program features
      if (this.screen.program) {
        this.screen.program.setMouse = () => {}; // Permanently disable mouse
        this.screen.program.normalBuffer = () => {}; // Prevent buffer switching
        this.screen.program.alternateBuffer = () => {}; // Prevent buffer switching
      }

    } catch (error) {
      console.error('Failed to initialize blessed screen:', error);
      return;
    }

    // Title bar
    this.titleBox = blessed.box({
      top: 0,
      left: 0,
      width: '100%',
      height: 3,
      content: '',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        bg: 'blue',
        border: {
          fg: 'cyan'
        }
      }
    });

    // Status dashboard (left side)
    this.statusBox = blessed.box({
      label: ' Status Dashboard ',
      top: 3,
      left: 0,
      width: '50%',
      height: '100%-3',
      content: '',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        border: {
          fg: 'cyan'
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'cyan'
        },
        style: {
          inverse: true
        }
      }
    });

    // Log panel (right side)
    this.logBox = blessed.log({
      label: ' Live Logs ',
      top: 3,
      left: '50%',
      width: '50%',
      height: '100%-3',
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        border: {
          fg: 'green'
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'green'
        },
        style: {
          inverse: true
        }
      },
      tags: true,
      clickable: false,
      input: true
    });

    // Add all elements to screen
    this.screen.append(this.titleBox);
    this.screen.append(this.statusBox);
    this.screen.append(this.logBox);

    // Key bindings
    this.screen.key(['escape', 'q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    this.screen.key(['r'], () => {
      this.refresh();
    });

    // Toggle mouse mode for copy/paste
    this.screen.key(['m', 'C-m'], () => {
      this.toggleMouseMode();
    });

    // Tab to switch focus between panels
    this.screen.key(['tab'], () => {
      if (this.screen.focused === this.logBox) {
        this.statusBox.focus();
      } else {
        this.logBox.focus();
      }
      this.updateFocusStyles();
      this.screen.render();
    });

    // Arrow keys for scrolling when focused
    this.screen.key(['up'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(-1);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(-1);
        this.screen.render();
      }
    });

    this.screen.key(['down'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(1);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(1);
        this.screen.render();
      }
    });

    this.screen.key(['pageup'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(-10);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(-10);
        this.screen.render();
      }
    });

    this.screen.key(['pagedown'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(10);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(10);
        this.screen.render();
      }
    });

    // Mouse click to focus panels (only in mouse interaction mode)
    this.statusBox.on('click', () => {
      if (this.mouseInteractionMode) {
        this.statusBox.focus();
        this.updateFocusStyles();
        this.screen.render();
      }
    });

    this.logBox.on('click', () => {
      if (this.mouseInteractionMode) {
        this.logBox.focus();
        this.updateFocusStyles();
        this.screen.render();
      }
    });

    // Focus events for visual feedback
    this.statusBox.on('focus', () => {
      this.updateFocusStyles();
    });

    this.logBox.on('focus', () => {
      this.updateFocusStyles();
    });

    // Mouse wheel scrolling (only in mouse interaction mode)
    this.statusBox.on('wheelup', () => {
      if (this.mouseInteractionMode) {
        this.statusBox.scroll(-3);
        this.screen.render();
      }
    });

    this.statusBox.on('wheeldown', () => {
      if (this.mouseInteractionMode) {
        this.statusBox.scroll(3);
        this.screen.render();
      }
    });

    this.logBox.on('wheelup', () => {
      if (this.mouseInteractionMode) {
        this.logBox.scroll(-3);
        this.screen.render();
      }
    });

    this.logBox.on('wheeldown', () => {
      if (this.mouseInteractionMode) {
        this.logBox.scroll(3);
        this.screen.render();
      }
    });

    // Focus on log box by default for scrolling
    this.logBox.focus();
  }

  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Update logger to suppress console output
    const { logger } = require('./logger');
    logger.updateForSplitScreen(true);

    // Initialize in mouse mode
    this.mouseInteractionMode = true;
    this.enableMouseInteraction();

    this.screen.render();

    // Start refresh timer
    this.refreshTimer = setInterval(() => {
      this.refresh();
    }, this.config.refreshRate);

    // Initial render
    this.refresh();
  }

  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    // Restore terminal to normal mode
    this.disableMouseInteraction();

    // Restore logger console output
    const { logger } = require('./logger');
    logger.updateForSplitScreen(false);

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = undefined;
    }

    if (this.screen) {
      this.screen.destroy();
    }
  }

  public updateDashboardData(data: Partial<DashboardData>): void {
    this.dashboardData = { ...this.dashboardData, ...data };
    if (this.isRunning) {
      this.refresh();
    }
  }

  public addLogEntry(entry: LogEntry): void {
    this.logEntries.push(entry);
    
    // Keep only recent entries
    if (this.logEntries.length > this.config.maxLogLines) {
      this.logEntries = this.logEntries.slice(-this.config.maxLogLines);
    }

    if (this.isRunning) {
      this.updateLogDisplay();
    }
  }

  public addLog(level: LogLevel, message: string, data?: any): void {
    const entry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      data
    };
    this.addLogEntry(entry);
  }

  private refresh(): void {
    if (!this.isRunning || !this.screen) {
      return;
    }

    try {
      // Capture and filter stdout during render to prevent artifacts
      const originalWrite = process.stdout.write;

      process.stdout.write = function(chunk: any, encoding?: any, callback?: any) {
        if (typeof chunk === 'string') {
          // Filter out artifact-causing sequences during render
          const filtered = chunk
            .replace(/\x1b\[\?1000[hl]/g, '') // Mouse tracking
            .replace(/\x1b\[\?1002[hl]/g, '') // Button events
            .replace(/\x1b\[\?1003[hl]/g, '') // Any events
            .replace(/\x1b\[1G\x1b\[0K/g, '') // Clear line artifacts
            .replace(/\x1b\[\d+;\d+H/g, '') // Cursor positioning artifacts
            .replace(/\x1b\[0m\x1b\[\d+;\d+m/g, ''); // Color reset artifacts

          // Only output if it's not just ANSI escape sequences
          if (filtered.trim() && !filtered.match(/^\x1b\[/)) {
            return originalWrite.call(this, filtered, encoding, callback);
          }
        }

        if (callback) callback();
        return true;
      };

      this.updateTitleDisplay();
      this.updateStatusDisplay();
      this.updateLogDisplay();

      // Single render call to minimize artifacts
      this.screen.render();

      // Restore stdout after render
      setTimeout(() => {
        process.stdout.write = originalWrite;
      }, 10);

    } catch (error: any) {
      // Restore stdout on error
      const originalWrite = process.stdout.write;
      process.stdout.write = originalWrite;

      // Silently handle render errors to prevent spam
      if (error?.message && !error.message.includes('destroyed')) {
        console.warn('Dashboard render error:', error.message);
      }
    }
  }

  private updateTitleDisplay(): void {
    const modeText = this.mouseInteractionMode ?
      'Mouse Mode: Scrolling & Navigation' :
      'Text Mode: Copy/Paste Enabled';
    this.updateTitleWithMode(modeText);
  }

  private updateStatusDisplay(): void {
    const data = this.dashboardData;
    const now = Date.now();
    const uptime = this.formatUptime(data.uptime);
    const lastActivity = this.formatTimeSince(data.lastActivity);
    
    let content = '';
    
    // Status Overview
    content += '{bold}{yellow-fg}📊 STATUS OVERVIEW{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Status: ${data.isRunning ? '{green-fg}RUNNING{/}' : '{red-fg}STOPPED{/}'}\n`;
    content += `Network: {cyan-fg}${data.networkName}{/} | Block: {yellow-fg}${data.currentBlock}{/}\n`;
    content += `Uptime: {green-fg}${uptime}{/}\n`;
    content += `Last Activity: {gray-fg}${lastActivity}{/}\n\n`;

    // Strategy Status
    content += '{bold}{blue-fg}⚡ STRATEGY STATUS{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Flashloan: ${data.flashloanEnabled ? '{green-fg}ENABLED{/}' : '{red-fg}DISABLED{/}'}\n`;
    content += `MEV-Share: ${data.mevShareEnabled ? '{green-fg}ENABLED{/}' : '{red-fg}DISABLED{/}'}\n`;
    content += `Arbitrage: ${data.arbitrageEnabled ? '{green-fg}ENABLED{/}' : '{red-fg}DISABLED{/}'}\n\n`;

    // Statistics
    content += '{bold}{magenta-fg}📈 STATISTICS{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Total Transactions: {yellow-fg}${data.totalTransactions}{/}\n`;
    content += `Relevant: {green-fg}${data.relevantTransactions}{/}\n`;
    content += `Opportunities Found: {cyan-fg}${data.opportunitiesFound}{/}\n`;
    content += `Opportunities Executed: {green-fg}${data.opportunitiesExecuted}{/}\n`;
    content += `Total Profit: {green-fg}${ethers.formatEther(data.totalProfit)} ETH{/}\n`;
    content += `Avg Gas Price: {yellow-fg}${ethers.formatUnits(data.avgGasPrice, 'gwei')} gwei{/}\n`;
    content += `Errors: ${data.errors > 0 ? `{red-fg}${data.errors}{/}` : '{green-fg}0{/}'}\n\n`;

    // Configuration
    content += '{bold}{cyan-fg}⚙️  CONFIGURATION{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Token Pairs: {yellow-fg}${data.configuration.tokenPairs.join(', ') || 'None'}{/}\n`;
    content += `DEXes: {cyan-fg}${data.configuration.dexes.join(', ') || 'None'}{/}\n`;
    content += `Min Profit: {green-fg}${data.configuration.minProfitThreshold} ETH{/}\n`;
    content += `Max Gas: {yellow-fg}${data.configuration.maxGasPrice} gwei{/}\n\n`;

    // Recent Successful Transactions
    if (data.successfulTransactions.length > 0) {
      content += '{bold}{green-fg}💰 RECENT SUCCESSFUL TRANSACTIONS{/}\n';
      content += '─'.repeat(40) + '\n';
      
      const recent = data.successfulTransactions.slice(-5).reverse();
      for (const tx of recent) {
        const time = new Date(tx.timestamp).toLocaleTimeString();
        const profit = ethers.formatEther(tx.profit);
        const gasUsed = ethers.formatEther(tx.gasUsed);
        
        content += `{gray-fg}${time}{/} {green-fg}${tx.type.toUpperCase()}{/} `;
        content += `Profit: {green-fg}${profit} ETH{/} `;
        content += `Gas: {yellow-fg}${gasUsed} ETH{/}`;
        if (tx.confidence) {
          content += ` Conf: {cyan-fg}${tx.confidence}%{/}`;
        }
        if (tx.details) {
          content += ` {gray-fg}(${tx.details}){/}`;
        }
        content += '\n';
      }
    }

    this.statusBox.setContent(content);
  }

  private updateLogDisplay(): void {
    if (!this.logBox) {
      return;
    }

    try {
      // Add recent log entries to the log box
      const recentLogs = this.logEntries.slice(-20); // Show last 20 logs

      for (const entry of recentLogs) {
        const timestamp = new Date(entry.timestamp).toLocaleTimeString();
        const levelColor = this.getLogLevelColor(entry.level);
        const levelText = entry.level.toUpperCase().padEnd(5);

        // Sanitize message to prevent ANSI injection
        const sanitizedMessage = this.sanitizeMessage(entry.message);

        let logLine = `{gray-fg}${timestamp}{/} {${levelColor}}${levelText}{/} ${sanitizedMessage}`;

        if (entry.data) {
          try {
            const dataStr = JSON.stringify(entry.data);
            logLine += ` {gray-fg}${this.sanitizeMessage(dataStr)}{/}`;
          } catch (error) {
            // Skip invalid data
          }
        }

        // Use blessed's internal method to add log without triggering screen updates
        this.logBox.log(logLine);
      }

      // Clear processed entries to avoid duplicates
      this.logEntries = [];
    } catch (error) {
      // Silently handle log display errors to prevent cascading issues
    }
  }

  private sanitizeMessage(message: string): string {
    if (typeof message !== 'string') {
      return String(message);
    }

    // Remove ANSI escape sequences and control characters that cause artifacts
    return message
      .replace(/\x1b\[[0-9;]*[a-zA-Z]/g, '') // Remove ANSI escape sequences
      .replace(/[\x00-\x1f\x7f-\x9f]/g, '') // Remove control characters
      .replace(/\{[^}]*\}/g, (match) => {
        // Only allow blessed tags, remove others that might cause artifacts
        if (match.match(/^\{(\/|[a-z-]+)\}$/)) {
          return match;
        }
        return '';
      });
  }

  private getLogLevelColor(level: LogLevel): string {
    switch (level) {
      case LogLevel.ERROR:
        return 'red-fg';
      case LogLevel.WARN:
        return 'yellow-fg';
      case LogLevel.INFO:
        return 'cyan-fg';
      case LogLevel.DEBUG:
        return 'gray-fg';
      default:
        return 'white-fg';
    }
  }

  private formatUptime(uptime: number): string {
    if (uptime === 0) return '0s';
    
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  private formatTimeSince(timestamp: number): string {
    if (timestamp === 0) return 'Never';

    const diff = Date.now() - timestamp;
    const seconds = Math.floor(diff / 1000);

    if (seconds < 60) {
      return `${seconds}s ago`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ago`;
    } else {
      return `${Math.floor(seconds / 3600)}h ago`;
    }
  }

  private updateFocusStyles(): void {
    // Update border colors based on focus
    if (this.screen.focused === this.statusBox) {
      this.statusBox.style.border.fg = 'yellow';
      this.logBox.style.border.fg = 'green';
    } else if (this.screen.focused === this.logBox) {
      this.statusBox.style.border.fg = 'cyan';
      this.logBox.style.border.fg = 'yellow';
    } else {
      this.statusBox.style.border.fg = 'cyan';
      this.logBox.style.border.fg = 'green';
    }
  }

  private toggleMouseMode(): void {
    this.mouseInteractionMode = !this.mouseInteractionMode;

    if (this.mouseInteractionMode) {
      // Enable mouse interaction mode (scrolling, clicking)
      this.enableMouseInteraction();
      this.updateTitleWithMode('Mouse Mode: Scrolling & Navigation');
      this.hideTextModeInstructions();
    } else {
      // Disable mouse interaction for text selection
      this.disableMouseInteraction();
      this.updateTitleWithMode('Text Mode: Mouse selection enabled - Press m to return');
      this.showTextModeInstructions();
    }

    this.screen.render();
  }

  private showTextModeInstructions(): void {
    // Add instructions to the log panel
    this.logBox.log('{yellow-fg}═══════════════════════════════════════{/}');
    this.logBox.log('{yellow-fg}📋 TEXT SELECTION MODE ENABLED{/}');
    this.logBox.log('{yellow-fg}═══════════════════════════════════════{/}');
    this.logBox.log('{green-fg}✅ You can now select text with your mouse{/}');
    this.logBox.log('{green-fg}✅ Click and drag to select log text{/}');
    this.logBox.log('{green-fg}✅ Use Ctrl+C to copy selected text{/}');
    this.logBox.log('{green-fg}✅ Press "m" to return to mouse mode{/}');
    this.logBox.log('{yellow-fg}═══════════════════════════════════════{/}');
    this.logBox.log('');
  }

  private hideTextModeInstructions(): void {
    // Instructions will naturally scroll away as new logs come in
    // No need to actively remove them
  }

  private enableMouseInteraction(): void {
    // Use blessed's built-in mouse handling to prevent artifacts
    if (!this.screen || !process.stdout.isTTY) {
      return;
    }

    try {
      // Use blessed's program interface instead of raw escape sequences
      if (this.screen.program && typeof this.screen.program.enableMouse === 'function') {
        this.screen.program.enableMouse();
      } else if (typeof this.screen.enableMouse === 'function') {
        this.screen.enableMouse();
      }
      // Don't write raw escape sequences - they cause the artifacts
    } catch (error: any) {
      // Silently handle mouse interaction errors to prevent console spam
    }
  }

  private disableMouseInteraction(): void {
    // Use blessed's built-in mouse handling to prevent artifacts
    if (!this.screen || !process.stdout.isTTY) {
      return;
    }

    try {
      // Use blessed's program interface instead of raw escape sequences
      if (this.screen.program && typeof this.screen.program.disableMouse === 'function') {
        this.screen.program.disableMouse();
      } else if (typeof (this.screen as any).disableMouse === 'function') {
        (this.screen as any).disableMouse();
      }
      // Don't write raw escape sequences - they cause the artifacts
    } catch (error: any) {
      // Silently handle mouse interaction errors to prevent console spam
    }
  }

  private updateTitleWithMode(modeText: string): void {
    const now = new Date();
    const uptime = this.formatUptime(this.dashboardData.uptime);
    const status = this.dashboardData.isRunning ? '{green-fg}RUNNING{/}' : '{red-fg}STOPPED{/}';

    const title = `{center}{bold}🤖 MEV Bot Dashboard - ${status} | ${modeText} | Press 'm' to toggle | ${now.toLocaleTimeString()}{/}`;
    this.titleBox.setContent(title);
  }

  public getScreen(): blessed.Widgets.Screen {
    return this.screen;
  }

  public isActive(): boolean {
    return this.isRunning;
  }
}

export const splitScreenDashboard = new SplitScreenDashboard();
