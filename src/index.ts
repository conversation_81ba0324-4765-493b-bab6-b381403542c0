import { ethers } from 'ethers';
import { MEVBot } from './core/bot';
import { SimpleMEVBot, runSimpleBot } from './simple-bot';
import { logger } from './utils/logger';
import { enhancedLogger } from './utils/enhancedLogger';
import { config } from './config';

function isTerminalCompatible(): boolean {
  // Check if we're in a proper terminal environment
  if (!process.stdout.isTTY || !process.stdin.isTTY) {
    return false;
  }

  // Check if we're in CI or test environment
  if (process.env.CI || process.env.NODE_ENV === 'test') {
    return false;
  }

  // Check terminal type
  const term = process.env.TERM;
  if (!term || term === 'dumb') {
    return false;
  }

  // Check if terminal supports colors and cursor positioning
  try {
    const tty = require('tty');
    if (!tty.isatty(process.stdout.fd)) {
      return false;
    }
  } catch (error) {
    return false;
  }

  return true;
}

async function main() {
  // Initialize split-screen dashboard early if explicitly enabled and supported
  if (process.env.SPLIT_SCREEN_DASHBOARD === 'true') {
    try {
      // Check terminal compatibility first
      if (isTerminalCompatible()) {
        console.log('🔧 Initializing split screen dashboard...');
        const { splitScreenDashboard } = await import('./utils/splitScreenDashboard');

        // Start split screen dashboard immediately to capture all logs
        splitScreenDashboard.start();

        // Give it a moment to initialize
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('✅ Split screen dashboard initialized successfully');
      } else {
        console.log('⚠️  Terminal not compatible with split screen mode, using standard logging...');
      }
    } catch (error) {
      console.warn('❌ Failed to initialize split screen dashboard, falling back to standard logging:', error);
    }
  } else {
    console.log('📝 Using standard logging mode (set SPLIT_SCREEN_DASHBOARD=true for dashboard)');
  }

  logger.info('🤖 Initializing Advanced MEV Bot...');

  try {
    // Use the full MEV bot implementation
    const bot = new MEVBot();

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await bot.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await bot.stop();
      process.exit(0);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.logError(error, 'UncaughtException');
      bot.emergencyStop();
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', { promise, reason });
      bot.emergencyStop();
      process.exit(1);
    });

    // Start the bot
    await bot.start();

    // Status is now handled by the live dashboard - no need for periodic logs

    logger.info('🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.');

  } catch (error) {
    logger.logError(error as Error, 'main');

    // Fallback to simple bot if advanced bot fails
    logger.warn('⚠️  Advanced MEV Bot failed, falling back to Simple Bot...');
    try {
      await runSimpleBot();
    } catch (fallbackError) {
      logger.logError(fallbackError as Error, 'fallback');
      process.exit(1);
    }
  }
}

// Start the application
main().catch((error) => {
  logger.logError(error, 'main');
  process.exit(1);
});
